package main

import (
	"context"
	"fmt"
	"io"
	"log"
	"os"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/transcribestreaming"
	"github.com/aws/aws-sdk-go-v2/service/transcribestreaming/types"
)

const (
	accessKey = "YOUR_ACCESS_KEY_ID"
	secretKey = "YOUR_SECRET_ACCESS_KEY"
	region    = "us-west-2"
)

func main() {
	log.Println("===> 加载 AWS 配置")
	cfg, err := config.LoadDefaultConfig(context.TODO(),
		config.WithRegion(region),
		config.WithCredentialsProvider(
			credentials.NewStaticCredentialsProvider(accessKey, secretKey, ""),
		),
	)
	if err != nil {
		log.Fatalf("无法加载 AWS 配置: %v", err)
	}

	client := transcribestreaming.NewFromConfig(cfg)

	// 打开 WAV 文件并跳过 44 字节头
	log.Println("===> 打开并跳过 WAV 头")
	f, err := os.Open("test.wav")
	if err != nil {
		log.Fatalf("打开音频文件失败: %v", err)
	}
	defer f.Close()
	if _, err := f.Seek(44, io.SeekStart); err != nil {
		log.Fatalf("跳过 WAV header 失败: %v", err)
	}

	// 发起流式转写请求
	log.Println("===> 发起 StartStreamTranscription")
	in := &transcribestreaming.StartStreamTranscriptionInput{
		LanguageCode:         types.LanguageCodeZhCn,
		MediaEncoding:        types.MediaEncodingPcm,
		MediaSampleRateHertz: aws.Int32(16000),
	}
	out, err := client.StartStreamTranscription(context.Background(), in)
	if err != nil {
		log.Fatalf("StartStreamTranscription 失败: %v", err)
	}
	stream := out.GetStream()

	// 并发：发送音频
	go func() {
		log.Println("===> 开始发送音频流")
		buf := make([]byte, 16*1024)
		for {
			n, err := f.Read(buf)
			if err != nil {
				if err == io.EOF {
					log.Println("音频读取 EOF，关闭发送端")
				} else {
					log.Printf("读取音频错误: %v", err)
				}
				stream.Close()
				return
			}
			log.Printf("发送 %d 字节音频", n)
			snd := &types.AudioStreamMemberAudioEvent{
				Value: types.AudioEvent{AudioChunk: buf[:n]},
			}
			if err := stream.Send(context.Background(), snd); err != nil {
				log.Printf("发送音频失败: %v", err)
				return
			}
		}
	}()

	// 主协程：接收结果
	log.Println("===> 开始接收转写事件")
	for evt := range stream.Events() {
		log.Printf("收到事件: %T", evt)
		switch e := evt.(type) {
		case *types.TranscriptResultStreamMemberTranscriptEvent:
			for _, res := range e.Value.Transcript.Results {
				if res.IsPartial {
					log.Println("  中间结果（partial）跳过")
				} else {
					for _, alt := range res.Alternatives {
						fmt.Println(">>", *alt.Transcript)
					}
				}
			}
		default:
			// 你也能在这里看到其他事件类型，比如 "SessionStartedEvent" 等
		}
	}
	if err := stream.Err(); err != nil {
		log.Fatalf("流出错: %v", err)
	}
	log.Println("===> 流式转写结束")
}
